import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Tool, ToolExecution, Agent, Workflow } from '@database/entities';
import { ToolController } from './tool.controller';
import { ToolService } from './tool.service';
import { ToolExecutionEngine } from './tool-execution.engine';
import { SessionModule } from '../session/session.module';
import { WebsocketModule } from '../websocket/websocket.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Tool, ToolExecution, Agent, Workflow]),
    SessionModule,
    WebsocketModule,
  ],
  controllers: [ToolController],
  providers: [ToolService, ToolExecutionEngine],
  exports: [ToolService, ToolExecutionEngine],
})
export class ToolModule {}
