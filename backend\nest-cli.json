{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "src", "compilerOptions": {"deleteOutDir": true, "webpack": true, "tsConfigPath": "tsconfig.build.json"}, "projects": {"gateway": {"type": "application", "root": "apps/gateway", "entryFile": "main", "sourceRoot": "apps/gateway/src", "compilerOptions": {"tsConfigPath": "apps/gateway/tsconfig.app.json"}}, "auth-service": {"type": "application", "root": "apps/auth-service", "entryFile": "main", "sourceRoot": "apps/auth-service/src", "compilerOptions": {"tsConfigPath": "apps/auth-service/tsconfig.app.json"}}, "agent-service": {"type": "application", "root": "apps/agent-service", "entryFile": "main", "sourceRoot": "apps/agent-service/src", "compilerOptions": {"tsConfigPath": "apps/agent-service/tsconfig.app.json"}}, "tool-service": {"type": "application", "root": "apps/tool-service", "entryFile": "main", "sourceRoot": "apps/tool-service/src", "compilerOptions": {"tsConfigPath": "apps/tool-service/tsconfig.app.json"}}, "workflow-service": {"type": "application", "root": "apps/workflow-service", "entryFile": "main", "sourceRoot": "apps/workflow-service/src", "compilerOptions": {"tsConfigPath": "apps/workflow-service/tsconfig.app.json"}}, "knowledge-service": {"type": "application", "root": "apps/knowledge-service", "entryFile": "main", "sourceRoot": "apps/knowledge-service/src", "compilerOptions": {"tsConfigPath": "apps/knowledge-service/tsconfig.app.json"}}, "notification-service": {"type": "application", "root": "apps/notification-service", "entryFile": "main", "sourceRoot": "apps/notification-service/src", "compilerOptions": {"tsConfigPath": "apps/notification-service/tsconfig.app.json"}}, "billing-service": {"type": "application", "root": "apps/billing-service", "entryFile": "main", "sourceRoot": "apps/billing-service/src", "compilerOptions": {"tsConfigPath": "apps/billing-service/tsconfig.app.json"}}, "analytics-service": {"type": "application", "root": "apps/analytics-service", "entryFile": "main", "sourceRoot": "apps/analytics-service/src", "compilerOptions": {"tsConfigPath": "apps/analytics-service/tsconfig.app.json"}}}}